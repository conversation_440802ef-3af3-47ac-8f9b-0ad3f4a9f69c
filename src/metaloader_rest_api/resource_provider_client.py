import itertools
import re
from ssl import create_default_context
from typing import Iterator
from urllib.parse import quote

import structlog
from httpx import Client, HTTPTransport

from metaloader_rest_api.helpers import ClearErrorIfNotInitialized, JsonDict
from metaloader_rest_api.loggable import Loggable

logger = structlog.stdlib.get_logger(__name__)


class ResourceProviderClient(Loggable):
    _DEFAULT_PAGE_SIZE = 100

    _http: Client = ClearErrorIfNotInitialized()

    class _paths:
        resource_list = "/resources"

    def __init__(
        self,
        base_url: str,
        timeout: int = 30,
        retries: int = 3,
        logger: structlog.stdlib.BoundLogger = logger,
        ssl_verify: bool = True,
    ):
        self._log = logger.bind(actor="resource_provider_client", url=base_url)
        self._log.info(
            "init",
            timeout=timeout,
            retries=retries,
            ssl_verify=ssl_verify,
        )
        if not re.search(r"/api/\d+(\.\d+)?/?$", base_url):
            self._log.warning("base_url doesn't contain API version")

        transport = HTTPTransport(
            verify=ssl_verify and create_default_context(),
            retries=retries,
        )
        self._http = Client(
            transport=transport,
            base_url=base_url,
            headers={"Content-Type": "application/json"},
            follow_redirects=True,
            timeout=timeout,
        )

    def __enter__(self):
        self._log.info("begin")
        self._http.__enter__()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self._http.__exit__(exc_type, exc_value, traceback)
        self._log.info("end")

    def _fetch_resource_cds_page(self, page_number: int) -> JsonDict:
        params = {
            "limit": self._DEFAULT_PAGE_SIZE,
            "offset": page_number * self._DEFAULT_PAGE_SIZE,
        }
        self._log.info("fetch_resource_cds_page", page_num=page_number, **params)
        response = self._http.get(url=self._paths.resource_list, params=params)
        response.raise_for_status()
        return response.json()

    def list_resource_cds(self) -> Iterator[str]:
        self._log.info("list_resource_cds")
        # NOTE: полагаемся на апи, что оно не станет отдавать бесконечный список resource_cd
        for current_page in itertools.count():
            payload = self._fetch_resource_cds_page(current_page)
            n_resource_cds = len(payload)
            self._log.info("fetched_resource_cds", count=n_resource_cds)
            for item in payload:
                yield item["resource_cd"]
            if n_resource_cds < self._DEFAULT_PAGE_SIZE:
                break

    def get_resource(self, resource_cd: str) -> JsonDict:
        url = f"{self._paths.resource_list}/{quote(resource_cd, safe='')}"
        response = self._http.get(url)
        response.raise_for_status()
        resource_definition = response.json()
        return resource_definition

    def get_all_resources(self) -> Iterator[tuple[str, JsonDict]]:
        for resource_cd in self.list_resource_cds():
            yield resource_cd, self.get_resource(resource_cd)
